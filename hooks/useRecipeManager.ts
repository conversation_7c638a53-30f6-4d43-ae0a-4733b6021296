import { useState, useEffect, useCallback, useRef } from 'react';
import { auth } from '@/firebase/firebaseConfig';
import { firestoreRepository, FirestoreCollections } from '@/repositories/firestoreRepository';
import { FirebaseFirestoreTypes } from '@react-native-firebase/firestore';
import analyticsService from '@/services/AnalyticsService';
import performanceService from '@/services/PerformanceService';
import logger from '@/services/logger';

// Type alias for react-native-firebase
type QueryDocumentSnapshot = FirebaseFirestoreTypes.QueryDocumentSnapshot<FirebaseFirestoreTypes.DocumentData>;
import {
  Recipe,
  DietPreference,
  MealType,
  RecipeSource,
  FilterMealType,
  InstructionType,
  numRecipesPerMealTypeInitialState,
} from '@/components/types';
import { generateRecipeBasicsAsync, generateRecipeDetailsAsync } from '@/services/generateRecipes';
import { RecipeService } from '@/services/RecipeService';
import { SavedRecipesService } from '@/services/SavedRecipesService';
import { MAX_RECIPES_PER_MEAL_TYPE } from '@/constants/RecipeConstants';
import { useInventory } from '@/contexts/InventoryContext';
import { createIngredientsWithAvailability } from '@/utils/ingredientUtils';

export interface UseRecipeManagerReturn {
  // State
  forYouRecipes: Recipe[];
  fromInventoryRecipes: Recipe[];
  savedRecipes: Recipe[];
  isLoading: boolean;
  refreshing: boolean;
  loadingMore: boolean;
  error: string | null;
  selectedSource: RecipeSource;
  selectedMealType: FilterMealType;
  expandedRecipeIds: Set<string>;
  loadingRecipeDetails: string | null;
  loadedDetailRecipeIds: Set<string>;
  lastResponseId: string | null;
  recipesPerMealType: { [key in MealType]: number };
  isPollingForFirstTimeRecipes: boolean;

  // Actions
  setSelectedSource: (source: RecipeSource) => void;
  setSelectedMealType: (mealType: FilterMealType) => void;
  setExpandedRecipeIds: React.Dispatch<React.SetStateAction<Set<string>>>;
  getRecipes: () => Promise<void>;
  loadMoreRecipes: () => Promise<void>;
  onRefresh: () => void;
  toggleExpanded: (recipeId: string) => Promise<void>;
  refreshSavedRecipes: () => Promise<void>;
  updateRecipeDetails: (recipeId: string, ingredients: any[], instructions: any) => void;

  // Computed
  currentRecipes: Recipe[];
  filteredRecipes: Recipe[];
  canLoadMore: boolean;
}

export const useRecipeManager = (): UseRecipeManagerReturn => {
  // Contexts
  const { inventoryItems, isIngredientAvailable } = useInventory();

  // State
  const [forYouRecipes, setForYouRecipes] = useState<Recipe[]>([]);
  const [fromInventoryRecipes, setFromInventoryRecipes] = useState<Recipe[]>([]);
  const [savedRecipes, setSavedRecipes] = useState<Recipe[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedSource, setSelectedSource] = useState<RecipeSource>(RecipeSource.FOR_YOU);
  const [selectedMealType, setSelectedMealType] = useState<FilterMealType>('All');
  const [expandedRecipeIds, setExpandedRecipeIds] = useState<Set<string>>(new Set());
  const [loadingRecipeDetails, setLoadingRecipeDetails] = useState<string | null>(null);
  const [loadedDetailRecipeIds, setLoadedDetailRecipeIds] = useState<Set<string>>(new Set());
  const [lastResponseId, setLastResponseId] = useState<string | null>(null);
  const [recipesPerMealType, setRecipesPerMealType] = useState<{ [key in MealType]: number }>(
    numRecipesPerMealTypeInitialState
  );
  const [isPollingForFirstTimeRecipes, setIsPollingForFirstTimeRecipes] = useState(false);

  // Pagination state for "For You" tab
  const [forYouPageSize] = useState(10);
  const [forYouLastVisible, setForYouLastVisible] = useState<QueryDocumentSnapshot | null>(null);
  const [forYouHasMore, setForYouHasMore] = useState(true);

  // Refs for polling
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const hasInitiallyLoadedRef = useRef(false);
  const isFetchingFirestoreRecipesRef = useRef(false);

  // Clear polling interval
  const clearPolling = useCallback(() => {
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current);
      pollingIntervalRef.current = null;
    }
  }, []);

  // Fetch pre-generated recipes from Firestore (For You tab)
  const getFirestoreRecipes = async (isInitialLoad = false, isLoadMore = false) => {
    try {
      const user = auth.currentUser;
      if (!user) return;

      // Prevent concurrent calls to avoid duplicates
      if (isFetchingFirestoreRecipesRef.current) {
        console.log('Already fetching Firestore recipes, skipping...');
        return;
      }

      isFetchingFirestoreRecipesRef.current = true;
      console.log('Fetching Firestore recipes...', { isInitialLoad, isLoadMore });

      // For initial load, reset pagination state
      if (isInitialLoad && !isLoadMore) {
        setForYouLastVisible(null);
        setForYouHasMore(true);
      }

      // Use paginated fetch
      const paginationResult = await RecipeService.fetchRecipesFromFirestorePaginated(
        user.uid,
        forYouPageSize,
        isLoadMore ? forYouLastVisible : null
      );

      if (paginationResult.data.length > 0) {
        console.log(`Firestore recipes loaded:`, paginationResult.data.length, 'recipes');

        if (isLoadMore) {
          // Append to existing recipes, but deduplicate by ID
          setForYouRecipes((prev) => {
            const existingIds = new Set(prev.map((r) => r.id));
            const newRecipes = paginationResult.data.filter((r) => !existingIds.has(r.id));
            return [...prev, ...newRecipes];
          });
        } else {
          // Replace existing recipes
          setForYouRecipes(paginationResult.data);
        }

        // Update pagination state
        setForYouLastVisible(paginationResult.lastVisible);
        setForYouHasMore(paginationResult.hasMore);

        setIsLoading(false);
        setIsPollingForFirstTimeRecipes(false);
        clearPolling();
        hasInitiallyLoadedRef.current = true;

        const firestoreRecipeIds = new Set(paginationResult.data.map((r) => r.id));
        if (isLoadMore) {
          setLoadedDetailRecipeIds((prev) => new Set([...prev, ...firestoreRecipeIds]));
        } else {
          setLoadedDetailRecipeIds(firestoreRecipeIds);
        }
      } else if (!hasInitiallyLoadedRef.current) {
        // If no recipes found and this is the first time, start polling
        if (selectedSource === RecipeSource.FOR_YOU || isInitialLoad) {
          setIsPollingForFirstTimeRecipes(true);
        }
        setIsLoading(false);
        setForYouHasMore(false);
      }
    } catch (error) {
      console.error('Error fetching Firestore recipes:', error);
      setIsLoading(false);
      setForYouHasMore(false);
    } finally {
      isFetchingFirestoreRecipesRef.current = false;
    }
  };

  // Fetch saved recipes (Favorites tab)
  const getSavedRecipes = async () => {
    try {
      const user = auth.currentUser;
      if (!user) return;

      console.log('Fetching saved recipes...');
      const recipes = await SavedRecipesService.getSavedRecipes();
      setSavedRecipes(recipes);
      console.log('Saved recipes loaded:', recipes.length);

      // Mark saved recipes with complete details as already loaded
      const savedRecipesIdsWithDetails = recipes
        .filter(
          (recipe) =>
            recipe.ingredients.length > 0 &&
            recipe.instructions[InstructionType.HIGH_LEVEL] !== '' &&
            recipe.instructions[InstructionType.HIGH_LEVEL] !== 'Instructions not available'
        )
        .map((recipe) => recipe.id);

      if (savedRecipesIdsWithDetails.length > 0) {
        setLoadedDetailRecipeIds((prev) => {
          const newSet = new Set(prev);
          savedRecipesIdsWithDetails.forEach((id) => newSet.add(id));
          return newSet;
        });
      }
    } catch (error) {
      console.error('Error fetching saved recipes:', error);
      setError('Failed to load saved recipes');
    }
  };

  // Fetch recipes from LLM based on inventory (From Inventory tab)
  const getLLMRecipes = async () => {
    let recipeGenerationTraceId = '';
    let startTime = 0;
    let ingredients: any[] = [];

    try {
      const user = auth.currentUser;
      if (!user) return;

      // Use inventory from context instead of fetching directly
      const availableInventoryItems = inventoryItems.filter((item) => item.name && item.quantity > 0);

      // Transform to ingredients with computed availability
      ingredients = createIngredientsWithAvailability(
        availableInventoryItems.map((item) => item.name),
        isIngredientAvailable
      );

      // Fetch diet preferences
      const dietPrefsDoc = (await firestoreRepository.getDocument(
        FirestoreCollections.DIET_PREFERENCES,
        user.uid
      )) as DietPreference | null;

      console.log('Fetching LLM recipes...');
      recipeGenerationTraceId = await performanceService.trackRecipeGeneration(
        Object.values(numRecipesPerMealTypeInitialState).reduce((a, b) => a + b, 0),
        'from_inventory'
      );
      startTime = Date.now();

      const { recipes: llmRecipes, responseId } = await generateRecipeBasicsAsync(
        ingredients,
        dietPrefsDoc,
        numRecipesPerMealTypeInitialState,
        fromInventoryRecipes.map((r) => r.title)
      );

      if (llmRecipes.length > 0) {
        const generationTime = Date.now() - startTime;
        logger.info(`LLM recipes loaded: ${llmRecipes.length} recipes`);

        // Track successful recipe generation
        await Promise.all([
          analyticsService.trackRecipeGenerated(llmRecipes.length, 'from_inventory', generationTime),
          performanceService.stopTrace(recipeGenerationTraceId, true, {
            recipe_count: llmRecipes.length.toString(),
            source: 'from_inventory',
          }),
        ]);

        setFromInventoryRecipes(llmRecipes);
        setLastResponseId(responseId);

        // Update recipe counters based on actual recipes received
        const recipeCountsByMealType: { [key in MealType]: number } = {
          [MealType.BREAKFAST]: 0,
          [MealType.LUNCH]: 0,
          [MealType.DINNER]: 0,
          [MealType.DESSERT]: 0,
        };

        llmRecipes.forEach((recipe) => {
          if (recipe.mealType && Object.values(MealType).includes(recipe.mealType as MealType)) {
            recipeCountsByMealType[recipe.mealType as MealType]++;
          }
        });

        setRecipesPerMealType(recipeCountsByMealType);
      } else {
        // Track failed recipe generation
        await performanceService.stopTrace(recipeGenerationTraceId, false, {
          reason: 'no_recipes_generated',
        });
      }
      setIsLoading(false);
    } catch (error) {
      const generationTime = Date.now() - startTime;

      // Track recipe generation error
      logger.dataError('recipe_generation', error instanceof Error ? error.message : 'Unknown error', {
        additionalData: {
          source: 'from_inventory',
          ingredientCount: ingredients.length,
        },
      });

      await Promise.all([
        performanceService.stopTrace(recipeGenerationTraceId, false, {
          error_message: error instanceof Error ? error.message : 'Unknown error',
        }),
        analyticsService.trackEvent('recipe_generation_error', {
          source: 'from_inventory',
          generation_time_ms: generationTime,
          error_message: error instanceof Error ? error.message : 'Unknown error',
        }),
      ]);
    }
  };

  // Main function that gets recipes based on selected source
  const getRecipes = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Reset recipes per meal type
      setRecipesPerMealType(numRecipesPerMealTypeInitialState);
      setLoadedDetailRecipeIds(new Set());

      // Reset pagination state for "For You" tab
      setForYouLastVisible(null);
      setForYouHasMore(true);

      // Clear existing recipes for all sources
      setForYouRecipes([]);
      setFromInventoryRecipes([]);
      setSavedRecipes([]);

      // Start all calls independently and simultaneously
      getFirestoreRecipes(true);
      getLLMRecipes();
      getSavedRecipes();
    } catch (error) {
      console.error('Error updating recipes:', error);
      setError('Failed to update recipes');
      setIsLoading(false);
    } finally {
      setRefreshing(false);
    }
  };

  const loadMoreRecipes = async () => {
    try {
      if (selectedSource === RecipeSource.FOR_YOU) {
        // Handle pagination for "For You" tab
        if (!forYouHasMore) return;

        setLoadingMore(true);
        await getFirestoreRecipes(false, true); // isInitialLoad=false, isLoadMore=true
        return;
      }

      // Handle pagination for "From Inventory" tab
      if (selectedSource !== RecipeSource.FROM_INVENTORY) return;

      // Don't load more if already at max recipes for the current meal type
      if (selectedMealType !== 'All' && recipesPerMealType[selectedMealType as MealType] >= MAX_RECIPES_PER_MEAL_TYPE) {
        return;
      }

      // Don't load more if all meal types have reached their max
      if (
        selectedMealType === 'All' &&
        Object.values(MealType).every((type) => recipesPerMealType[type] >= MAX_RECIPES_PER_MEAL_TYPE)
      ) {
        return;
      }

      setLoadingMore(true);
      const user = auth.currentUser;
      if (!user) throw new Error('User not authenticated');

      // Use inventory from context and fetch diet preferences
      const availableInventoryItems = inventoryItems.filter((item) => item.name && item.quantity > 0);
      const ingredients = createIngredientsWithAvailability(
        availableInventoryItems.map((item) => item.name),
        isIngredientAvailable
      );

      const dietPrefsDoc = (await firestoreRepository.getDocument(
        FirestoreCollections.DIET_PREFERENCES,
        user.uid
      )) as DietPreference | null;

      // Generate more recipes logic (simplified for brevity)
      if (ingredients.length > 0) {
        // Generate new recipes based on meal type selection
        const { recipes: newRecipes, responseId } = await generateRecipeBasicsAsync(
          ingredients,
          dietPrefsDoc,
          selectedMealType === 'All' ? numRecipesPerMealTypeInitialState : { [selectedMealType]: 1 },
          fromInventoryRecipes.map((r) => r.title)
        );

        setLastResponseId(responseId);

        // Only proceed if we actually got new recipes
        if (newRecipes.length > 0) {
          // First, determine which recipes are actually new by checking against current recipes
          const currentRecipeIds = new Set(fromInventoryRecipes.map((r) => r.id));
          const actuallyNewRecipes = newRecipes.filter((newRecipe) => !currentRecipeIds.has(newRecipe.id));

          // Only proceed if we have actually new recipes
          if (actuallyNewRecipes.length > 0) {
            // Count new recipes by meal type
            const addedRecipesByMealType: { [key in MealType]: number } = {
              [MealType.BREAKFAST]: 0,
              [MealType.LUNCH]: 0,
              [MealType.DINNER]: 0,
              [MealType.DESSERT]: 0,
            };

            actuallyNewRecipes.forEach((recipe) => {
              if (recipe.mealType && Object.values(MealType).includes(recipe.mealType as MealType)) {
                addedRecipesByMealType[recipe.mealType as MealType]++;
              }
            });

            // Add the new recipes to the state
            setFromInventoryRecipes((prevRecipes) => [...prevRecipes, ...actuallyNewRecipes]);

            // Update the counters based on recipes that were actually added
            setRecipesPerMealType((prevCounts) => {
              const updatedCounts = { ...prevCounts };

              // Update counters based on actual recipes added
              Object.entries(addedRecipesByMealType).forEach(([mealType, count]) => {
                if (count > 0) {
                  updatedCounts[mealType as MealType] = Math.min(
                    updatedCounts[mealType as MealType] + count,
                    MAX_RECIPES_PER_MEAL_TYPE
                  );
                }
              });

              return updatedCounts;
            });
          } else {
            // No new recipes were generated, increment counters to prevent infinite attempts
            setRecipesPerMealType((prevCounts) => {
              const updatedCounts = { ...prevCounts };

              if (selectedMealType === 'All') {
                // Increment all meal types that haven't reached max
                Object.values(MealType).forEach((mealType) => {
                  if (updatedCounts[mealType] < MAX_RECIPES_PER_MEAL_TYPE) {
                    updatedCounts[mealType]++;
                  }
                });
              } else {
                // Increment the selected meal type
                const mealType = selectedMealType as MealType;
                if (updatedCounts[mealType] < MAX_RECIPES_PER_MEAL_TYPE) {
                  updatedCounts[mealType]++;
                }
              }

              return updatedCounts;
            });
          }
        } else {
          // No recipes returned from API, increment counters to prevent infinite attempts
          setRecipesPerMealType((prevCounts) => {
            const updatedCounts = { ...prevCounts };

            if (selectedMealType === 'All') {
              // Increment all meal types that haven't reached max
              Object.values(MealType).forEach((mealType) => {
                if (updatedCounts[mealType] < MAX_RECIPES_PER_MEAL_TYPE) {
                  updatedCounts[mealType]++;
                }
              });
            } else {
              // Increment the selected meal type
              const mealType = selectedMealType as MealType;
              if (updatedCounts[mealType] < MAX_RECIPES_PER_MEAL_TYPE) {
                updatedCounts[mealType]++;
              }
            }

            return updatedCounts;
          });
        }
      } else {
        // No ingredients available, increment counters to prevent infinite attempts
        setRecipesPerMealType((prevCounts) => {
          const updatedCounts = { ...prevCounts };

          if (selectedMealType === 'All') {
            // Increment all meal types that haven't reached max
            Object.values(MealType).forEach((mealType) => {
              if (updatedCounts[mealType] < MAX_RECIPES_PER_MEAL_TYPE) {
                updatedCounts[mealType]++;
              }
            });
          } else {
            // Increment the selected meal type
            const mealType = selectedMealType as MealType;
            if (updatedCounts[mealType] < MAX_RECIPES_PER_MEAL_TYPE) {
              updatedCounts[mealType]++;
            }
          }

          return updatedCounts;
        });
      }
    } catch (error) {
      console.error('Error loading more recipes:', error);
    } finally {
      setLoadingMore(false);
    }
  };

  // Refresh function for "For You" tab only
  const onRefreshForYou = useCallback(async () => {
    try {
      setRefreshing(true);
      setLoadingMore(false); // Reset loadingMore to prevent ActivityIndicator
      setError(null);

      // Clear existing recipes to prevent duplicates during refresh
      setForYouRecipes([]);
      setLoadedDetailRecipeIds(new Set());

      // Reset pagination state
      setForYouLastVisible(null);
      setForYouHasMore(true);

      // Only fetch Firestore recipes for "For You" tab
      await getFirestoreRecipes(true);
    } catch (error) {
      console.error('Error refreshing For You recipes:', error);
      setError('Failed to refresh recipes');
    } finally {
      setRefreshing(false);
    }
  }, []);

  // Refresh function for "From Inventory" tab only
  const onRefreshFromInventory = useCallback(async () => {
    try {
      setRefreshing(true);
      setLoadingMore(false); // Reset loadingMore to prevent ActivityIndicator
      setError(null);

      // Reset recipes per meal type and clear existing inventory recipes
      setRecipesPerMealType(numRecipesPerMealTypeInitialState);
      setLoadedDetailRecipeIds(new Set());
      setFromInventoryRecipes([]);

      // Only fetch LLM recipes for "From Inventory" tab
      await getLLMRecipes();
    } catch (error) {
      console.error('Error refreshing From Inventory recipes:', error);
      setError('Failed to refresh recipes');
    } finally {
      setRefreshing(false);
    }
  }, []);

  // Refresh function for "Favorites" tab only
  const onRefreshFavorites = useCallback(async () => {
    try {
      setRefreshing(true);
      setLoadingMore(false); // Reset loadingMore to prevent ActivityIndicator
      setError(null);

      // Clear existing saved recipes and reset loaded detail IDs for favorites
      setSavedRecipes([]);
      setLoadedDetailRecipeIds((prev) => {
        const newSet = new Set(prev);
        // Remove saved recipe IDs from loaded details when refreshing
        savedRecipes.forEach((recipe) => newSet.delete(recipe.id));
        return newSet;
      });

      // Only fetch saved recipes for "Favorites" tab
      await getSavedRecipes();
    } catch (error) {
      console.error('Error refreshing Favorites recipes:', error);
      setError('Failed to refresh saved recipes');
    } finally {
      setRefreshing(false);
    }
  }, []);

  // Main refresh function that calls the appropriate tab-specific refresh
  const onRefresh = useCallback(() => {
    if (selectedSource === RecipeSource.FOR_YOU) {
      onRefreshForYou();
    } else if (selectedSource === RecipeSource.FROM_INVENTORY) {
      onRefreshFromInventory();
    } else if (selectedSource === RecipeSource.FAVORITES) {
      onRefreshFavorites();
    }
  }, [selectedSource, onRefreshForYou, onRefreshFromInventory, onRefreshFavorites]);

  // Function to update recipe details when they're generated during favoriting
  const updateRecipeDetails = useCallback((recipeId: string, ingredients: any[], instructions: any) => {
    // Update the recipe in the appropriate source
    setForYouRecipes((prevRecipes) =>
      prevRecipes.map((r) => (r.id === recipeId ? { ...r, ingredients, instructions } : r))
    );

    setFromInventoryRecipes((prevRecipes) =>
      prevRecipes.map((r) => (r.id === recipeId ? { ...r, ingredients, instructions } : r))
    );

    // Mark the recipe as having loaded details
    setLoadedDetailRecipeIds((prev) => {
      const newSet = new Set(prev);
      newSet.add(recipeId);
      return newSet;
    });

    console.log('Updated recipe details for recipe:', recipeId);
  }, []);

  const toggleExpanded = async (recipeId: string) => {
    // If we're already expanded, just collapse
    if (expandedRecipeIds.has(recipeId)) {
      setExpandedRecipeIds((prevIds) => {
        const newIds = new Set(prevIds);
        newIds.delete(recipeId);
        return newIds;
      });
      return;
    }

    // First, expand the recipe immediately to show the loading skeleton
    setExpandedRecipeIds((prevIds) => {
      const newIds = new Set(prevIds);
      newIds.add(recipeId);
      return newIds;
    });

    // Check if we need to fetch details and track recipe details viewed
    const currentRecipes =
      selectedSource === RecipeSource.FOR_YOU
        ? forYouRecipes
        : selectedSource === RecipeSource.FROM_INVENTORY
          ? fromInventoryRecipes
          : savedRecipes;
    const recipe = currentRecipes.find((r) => r.id === recipeId);

    // Track recipe details viewed
    if (recipe) {
      await analyticsService.trackRecipeDetailsViewed(recipeId, recipe.title, recipe.mealType);
    }
    const hasCompleteDetails =
      recipe &&
      recipe.ingredients.length > 0 &&
      recipe.instructions[InstructionType.HIGH_LEVEL] !== '' &&
      recipe.instructions[InstructionType.HIGH_LEVEL] !== 'Instructions not available';

    // If we need to fetch details, do it after expanding
    if (!loadedDetailRecipeIds.has(recipeId) && !hasCompleteDetails && lastResponseId) {
      setLoadingRecipeDetails(recipeId);

      try {
        const user = auth.currentUser;
        if (!user || !recipe) throw new Error('User not authenticated or recipe not found');

        const details = await generateRecipeDetailsAsync(recipe.id, recipe.title, recipe.mealType, lastResponseId);

        // Update the recipe with the details
        if (selectedSource === RecipeSource.FOR_YOU) {
          setForYouRecipes((prevRecipes) =>
            prevRecipes.map((r) =>
              r.id === recipeId ? { ...r, ingredients: details.ingredients, instructions: details.instructions } : r
            )
          );
        } else if (selectedSource === RecipeSource.FROM_INVENTORY) {
          setFromInventoryRecipes((prevRecipes) =>
            prevRecipes.map((r) =>
              r.id === recipeId ? { ...r, ingredients: details.ingredients, instructions: details.instructions } : r
            )
          );
        } else if (selectedSource === RecipeSource.FAVORITES) {
          setSavedRecipes((prevRecipes) =>
            prevRecipes.map((r) =>
              r.id === recipeId ? { ...r, ingredients: details.ingredients, instructions: details.instructions } : r
            )
          );
        }

        setLoadedDetailRecipeIds((prev) => {
          const newSet = new Set(prev);
          newSet.add(recipeId);
          return newSet;
        });
      } catch (error) {
        console.error('Error loading recipe details:', error);
      } finally {
        setLoadingRecipeDetails(null);
      }
    } else if (hasCompleteDetails) {
      setLoadedDetailRecipeIds((prev) => {
        const newSet = new Set(prev);
        newSet.add(recipeId);
        return newSet;
      });
    }
  };

  // Polling effect for first-time recipe loading
  useEffect(() => {
    if (isPollingForFirstTimeRecipes && selectedSource === RecipeSource.FOR_YOU && forYouRecipes.length === 0) {
      logger.info('Starting polling for first-time recipes...');
      pollingIntervalRef.current = setInterval(() => {
        logger.debug('Polling for recipes...');
        getFirestoreRecipes(true); // Pass isInitialLoad=true to ensure proper state reset
      }, 5000); // Poll every 5 seconds

      return () => clearPolling();
    } else {
      clearPolling();
    }
  }, [isPollingForFirstTimeRecipes, selectedSource, forYouRecipes.length, clearPolling]);

  // Initial fetch
  useEffect(() => {
    getRecipes();
  }, []);

  // Handle tab switching
  useEffect(() => {
    setSelectedMealType('All');

    // When switching to "For You" tab, check if we need to start/resume polling
    if (selectedSource === RecipeSource.FOR_YOU) {
      // If we don't have recipes and haven't loaded initially, start polling
      if (forYouRecipes.length === 0 && !hasInitiallyLoadedRef.current) {
        console.log('Switching to For You tab - starting polling for first-time recipes');
        setIsPollingForFirstTimeRecipes(true);
        // Also trigger an immediate fetch
        getFirestoreRecipes(true);
      }
    } else if (selectedSource === RecipeSource.FROM_INVENTORY) {
      // When switching to "From Inventory" tab, fetch recipes if we don't have them
      if (fromInventoryRecipes.length === 0) {
        console.log('Switching to From Inventory tab - loading recipes');
        setIsLoading(true);
        setError(null);
        getLLMRecipes();
      }
      // Stop polling when switching away from "For You" tab
      clearPolling();
    } else if (selectedSource === RecipeSource.FAVORITES) {
      // When switching to Favorites tab, fetch saved recipes if we don't have them
      if (savedRecipes.length === 0) {
        getSavedRecipes();
      }
      // Stop polling when switching away from "For You" tab
      clearPolling();
    } else {
      // Only stop polling when switching away from "For You" tab, but preserve the state
      clearPolling();
    }
  }, [selectedSource, forYouRecipes.length, fromInventoryRecipes.length, savedRecipes.length, clearPolling]);

  // Cleanup polling on unmount
  useEffect(() => {
    return () => {
      clearPolling();
    };
  }, [clearPolling]);

  // Computed values
  const currentRecipes =
    selectedSource === RecipeSource.FOR_YOU
      ? forYouRecipes
      : selectedSource === RecipeSource.FROM_INVENTORY
        ? fromInventoryRecipes
        : savedRecipes;

  const filteredRecipes = currentRecipes.filter((recipe) => {
    if (selectedMealType !== 'All' && recipe.mealType !== selectedMealType) {
      return false;
    }
    return true;
  });

  const canLoadMore =
    (selectedSource === RecipeSource.FOR_YOU && forYouHasMore) ||
    (selectedSource === RecipeSource.FROM_INVENTORY &&
      (selectedMealType === 'All'
        ? Object.values(MealType).some((type) => recipesPerMealType[type] < MAX_RECIPES_PER_MEAL_TYPE)
        : recipesPerMealType[selectedMealType as MealType] < MAX_RECIPES_PER_MEAL_TYPE));

  return {
    // State
    forYouRecipes,
    fromInventoryRecipes,
    savedRecipes,
    isLoading,
    refreshing,
    loadingMore,
    error,
    selectedSource,
    selectedMealType,
    expandedRecipeIds,
    loadingRecipeDetails,
    loadedDetailRecipeIds,
    lastResponseId,
    recipesPerMealType,
    isPollingForFirstTimeRecipes,

    // Actions
    setSelectedSource,
    setSelectedMealType,
    setExpandedRecipeIds,
    getRecipes,
    loadMoreRecipes,
    onRefresh,
    toggleExpanded,
    refreshSavedRecipes: getSavedRecipes,
    updateRecipeDetails,

    // Computed
    currentRecipes,
    filteredRecipes,
    canLoadMore,
  };
};
